"use client";

import { useCallback, useEffect, useState } from "react";
import { Trash2 } from "lucide-react";

import { deleteCollection, getCollections } from "@/api/collection-api";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Collection,
  COLLECTION_STATUS_TEXT,
  CollectionStatus,
} from "@/core.constants";
import { usePaginationRequest } from "@/hooks/use-pagination-request";
import { ManageCollectionModal } from "./manage-collection-modal";
import Image from "next/image";
import { getImagePathWithFallback } from "@/utils/image-path";
import { firebaseTimestampToDate } from "@/utils/date-utils";

type StatusBadgeProps = {
  status: CollectionStatus;
};

const StatusBadge = ({ status }: StatusBadgeProps) => {
  const getStatusColor = (status: CollectionStatus) => {
    switch (status) {
      case CollectionStatus.MARKET:
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case CollectionStatus.PREMARKET:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      case CollectionStatus.DELETED:
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  return (
    <span
      className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(
        status
      )}`}
    >
      {COLLECTION_STATUS_TEXT[status]}
    </span>
  );
};

export const CollectionManagement = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingCollection, setEditingCollection] = useState<Collection | null>(
    null
  );
  const [isDeleting, setIsDeleting] = useState(false);

  const fetchCollectionsWrapper = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    async (pageSize: number, lastDoc?: any) => {
      const result = await getCollections(pageSize, lastDoc);
      return {
        items: result.collections,
        lastDoc: result.lastDoc,
        hasMore: result.hasMore,
      };
    },
    []
  );

  const {
    items: collections,
    loading,
    hasMore,
    loadItems,
    reset,
  } = usePaginationRequest(fetchCollectionsWrapper, { pageSize: 10 });

  useEffect(() => {
    loadItems(true);
  }, [loadItems]);

  const handleAddCollection = () => {
    setEditingCollection(null);
    setIsModalOpen(true);
  };

  const handleEditCollection = (collection: Collection) => {
    setEditingCollection(collection);
    setIsModalOpen(true);
  };

  const handleDeleteCollection = async (collection: Collection) => {
    setIsDeleting(true);
    try {
      await deleteCollection(collection.id);
      reset();
      loadItems(true);
    } catch (error) {
      console.error("Error deleting collection:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCollectionSaved = () => {
    setIsModalOpen(false);
    setEditingCollection(null);
    reset();
    loadItems(true);
  };

  const loadingMarkup = loading ? (
    <div className="flex justify-center py-4">
      <div className="text-sm text-muted-foreground">
        Loading collections...
      </div>
    </div>
  ) : null;

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">
          Collection Management
        </h1>
        <Button onClick={handleAddCollection}>Add Collection</Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Image</TableHead>
              <TableHead>Collection ID</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Launched At</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {collections.map((collection) => (
              <TableRow key={collection.id}>
                <TableCell>
                  <div className="relative w-8 h-8 rounded-sm overflow-hidden bg-slate-700 flex-shrink-0">
                    <Image
                      src={
                        getImagePathWithFallback(collection.id, "png").primary
                      }
                      alt={collection.name}
                      fill
                      className="object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        const fallbackSrc = getImagePathWithFallback(
                          collection.id,
                          "png"
                        ).fallback;
                        if (target.src !== fallbackSrc) {
                          target.src = fallbackSrc;
                        } else {
                          target.style.display = "none";
                        }
                      }}
                    />
                  </div>
                </TableCell>
                <TableCell className="font-mono text-sm">
                  {collection.id}
                </TableCell>
                <TableCell className="font-medium">{collection.name}</TableCell>
                <TableCell className="max-w-xs truncate">
                  {collection.description}
                </TableCell>
                <TableCell>
                  <StatusBadge status={collection.status} />
                </TableCell>
                <TableCell className="text-sm text-muted-foreground">
                  {collection.launchedAt
                    ? new Date(
                        firebaseTimestampToDate(collection.launchedAt)
                      ).toLocaleDateString()
                    : "Not launched"}
                </TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditCollection(collection)}
                      className="cursor-pointer"
                    >
                      Edit
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950 cursor-pointer"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete Collection</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete &quot;
                            {collection.name}
                            &quot;? This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel className="cursor-pointer">
                            Cancel
                          </AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDeleteCollection(collection)}
                            disabled={isDeleting}
                            className="bg-red-600 hover:bg-red-700 text-white cursor-pointer"
                          >
                            {isDeleting ? "Deleting..." : "Delete"}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {loadingMarkup}

      {hasMore && !loading && (
        <div className="flex justify-center">
          <Button variant="outline" onClick={() => loadItems()}>
            Load More
          </Button>
        </div>
      )}

      <ManageCollectionModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        collection={editingCollection}
        onSave={handleCollectionSaved}
      />
    </div>
  );
};
