import { Timestamp } from "firebase/firestore";

export const firebaseTimestampToDate = (timestamp: Timestamp | Date): Date => {
  if (timestamp instanceof Date) {
    return timestamp;
  }

  return timestamp.toDate();
};

export const formatDateToFirebaseTimestamp = (
  date: Date | Timestamp
): Timestamp => {
  if (date instanceof Timestamp) {
    return date;
  }
  return Timestamp.fromDate(date);
};
