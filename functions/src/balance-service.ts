import * as admin from "firebase-admin";
import { UserBalance, UserEntity } from "./types";
import { roundToTwoDecimals, safeSubtract } from "./utils";

export async function getUserBalance(userId: string): Promise<UserBalance> {
  try {
    const db = admin.firestore();
    const userDoc = await db.collection("users").doc(userId).get();

    if (!userDoc.exists) {
      throw new Error(`User ${userId} not found`);
    }

    const userData = userDoc.data() as UserEntity;
    return userData.balance || { sum: 0, locked: 0 };
  } catch (error) {
    console.error(`Error getting balance for user ${userId}:`, error);
    throw error;
  }
}

export async function updateUserBalance(
  userId: string,
  amountChange: number,
  lockedChange: number = 0
): Promise<UserBalance> {
  try {
    const db = admin.firestore();
    const userRef = db.collection("users").doc(userId);

    return await db.runTransaction(
      async (transaction: admin.firestore.Transaction) => {
        const userDoc = await transaction.get(userRef);

        if (!userDoc.exists) {
          throw new Error(`User ${userId} not found`);
        }

        const userData = userDoc.data() as UserEntity;
        const currentBalance: UserBalance = userData.balance || {
          sum: 0,
          locked: 0,
        };

        // Calculate new balances
        const newSum = roundToTwoDecimals(currentBalance.sum + amountChange);
        const newLocked = roundToTwoDecimals(
          currentBalance.locked + lockedChange
        );

        // Validate that balances won't go negative
        if (newSum < 0) {
          throw new Error(
            `Insufficient balance: current ${currentBalance.sum} TON, attempting to change by ${amountChange} TON`
          );
        }

        if (newLocked < 0) {
          throw new Error(
            `Insufficient locked funds: current ${currentBalance.locked} TON, attempting to change by ${lockedChange} TON`
          );
        }

        // Validate that locked amount doesn't exceed total balance
        if (newLocked > newSum) {
          throw new Error(
            `Locked amount (${newLocked} TON) cannot exceed total balance (${newSum} TON)`
          );
        }

        const newBalance: UserBalance = {
          sum: newSum,
          locked: newLocked,
        };

        transaction.update(userRef, { balance: newBalance });

        return newBalance;
      }
    );
  } catch (error) {
    console.error(`Error updating balance for user ${userId}:`, error);
    throw error;
  }
}

export async function addFunds(
  userId: string,
  amount: number
): Promise<UserBalance> {
  if (amount <= 0) {
    throw new Error("Amount must be positive");
  }

  return await updateUserBalance(userId, amount, 0);
}

export async function lockFunds(
  userId: string,
  amount: number
): Promise<UserBalance> {
  if (amount <= 0) {
    throw new Error("Amount must be positive");
  }

  // Check if user has sufficient available balance to lock
  const currentBalance = await getUserBalance(userId);
  const availableBalance = safeSubtract(
    currentBalance.sum,
    currentBalance.locked
  );

  if (availableBalance < amount) {
    throw new Error(
      `Insufficient available balance to lock ${amount} TON. Available: ${availableBalance} TON`
    );
  }

  return await updateUserBalance(userId, 0, amount);
}

export async function unlockFunds(
  userId: string,
  amount: number
): Promise<UserBalance> {
  if (amount <= 0) {
    throw new Error("Amount must be positive");
  }

  // Check if user has sufficient locked funds to unlock
  const currentBalance = await getUserBalance(userId);

  if (currentBalance.locked < amount) {
    throw new Error(
      `Insufficient locked funds to unlock ${amount} TON. Locked: ${currentBalance.locked} TON`
    );
  }

  return await updateUserBalance(userId, 0, -amount);
}

export async function spendLockedFunds(
  userId: string,
  amount: number
): Promise<UserBalance> {
  if (amount <= 0) {
    throw new Error("Amount must be positive");
  }

  // Check if user has sufficient locked funds to spend
  const currentBalance = await getUserBalance(userId);

  if (currentBalance.locked < amount) {
    throw new Error(
      `Insufficient locked funds to spend ${amount} TON. Locked: ${currentBalance.locked} TON`
    );
  }

  if (currentBalance.sum < amount) {
    throw new Error(
      `Insufficient total balance to spend ${amount} TON. Balance: ${currentBalance.sum} TON`
    );
  }

  return await updateUserBalance(userId, -amount, -amount);
}

export async function hasAvailableBalance(
  userId: string,
  amount: number
): Promise<boolean> {
  try {
    const balance = await getUserBalance(userId);
    const availableBalance = safeSubtract(balance.sum, balance.locked);
    return availableBalance >= amount;
  } catch (error) {
    console.error(
      `Error checking available balance for user ${userId}:`,
      error
    );
    return false;
  }
}

/**
 * Validates that user has sufficient funds for an operation
 * Throws detailed error if insufficient funds
 */
export async function validateSufficientFunds(
  userId: string,
  amount: number,
  operation: string = "operation"
): Promise<void> {
  const balance = await getUserBalance(userId);
  const availableBalance = safeSubtract(balance.sum, balance.locked);

  if (availableBalance < amount) {
    throw new Error(
      `Insufficient funds for ${operation}: required ${amount} TON, available ${availableBalance} TON (total: ${balance.sum} TON, locked: ${balance.locked} TON)`
    );
  }
}

/**
 * Validates that user has sufficient locked funds for an operation
 * Throws detailed error if insufficient locked funds
 */
export async function validateSufficientLockedFunds(
  userId: string,
  amount: number,
  operation: string = "operation"
): Promise<void> {
  const balance = await getUserBalance(userId);

  if (balance.locked < amount) {
    throw new Error(
      `Insufficient locked funds for ${operation}: required ${amount} TON, locked ${balance.locked} TON`
    );
  }
}
