/* eslint-disable no-unused-vars */
import { Timestamp } from "firebase-admin/firestore";

export enum CollectionStatus {
  PREMARKET = "PREMARKET",
  MARKET = "MARKET",
  DELETED = "DELETED",
}

export interface Collection {
  id: string;
  name: string;
  description: string;
  status: CollectionStatus;
  launchedAt?: Timestamp;
  floorPrice: number; // Minimum floor price for collection items in TON
}

export interface UserBalance {
  sum: number;
  locked: number;
}

export interface UserEntity {
  id: string;
  email?: string | null;
  displayName?: string | null;
  photoURL?: string | null;
  role: "user" | "admin";
  tg_id?: string;
  ton_wallet_address?: string;
  raw_ton_wallet_address?: string;
  referral_id?: string; // Telegram ID of the user who referred this user
  referral_fee?: number; // Custom referral fee in BPS (basis points)
  balance?: UserBalance;
}

export enum OrderStatus {
  ACTIVE = "active",
  PAID = "paid",
  GIFT_SENT_TO_RELAYER = "gift_sent_to_relayer",
  FULFILLED = "fulfilled",
  CANCELLED = "cancelled",
}

export interface OrderEntity {
  id: string;
  number: number; // Auto-incremented order number
  buyerId?: string; // Optional since orders can be created without a buyer
  sellerId?: string; // Optional since orders can be created without a seller
  collectionId: string; // Collection ID for floor price validation
  amount: number;
  status: OrderStatus;
  deadline?: Timestamp; // Deadline for seller to fulfill the order
  giftSentToRelayerAt?: Timestamp; // When gift was sent to relayer
  owned_gift_id?: string; // ID of the gift owned by the user creating the order
  secondaryMarketPrice?: number | null; // Price set by buyer for reselling on secondary market
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface TxLookup {
  id: string;
  last_checked_record_id: string;
  updatedAt: Timestamp;
}

export interface AppConfig {
  depositFee: number; // Static TON value
  withdrawFee: number; // Static TON value
  referrer_fee: number; // in BPS (basis points)
  reject_order_fee: number; // in BPS
  purchase_fee: number; // in BPS
  buyer_lock_percentage: number; // Percentage of order amount locked by buyer (0.0-1.0)
  seller_lock_percentage: number; // Percentage of order amount locked by seller (0.0-1.0)
  min_withdrawal_amount: number; // Static TON value
  max_withdrawal_amount: number; // Static TON value
  min_secondary_market_price: number; // Minimum price for secondary market resale in TON
}
